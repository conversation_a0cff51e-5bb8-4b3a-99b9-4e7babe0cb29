/* You can add global styles to this file, and also import other style files */
@import 'styles.variables.scss';

/* Override DevExtreme card overflow to fix layout issues */
.dx-card,
.dx-swatch-additional .dx-card,
.dx-swatch-base .dx-card {
    overflow: visible !important;
}

body .ui-widget-overlay {
    opacity: 0.5;
}

.overview-boxes {
    display: flex;
    justify-content: space-evenly;
    width: 100%;
    flex-wrap: wrap;
    app-overview-box {
        flex-grow: 1;
        margin: 6.5px;
    }
}

// Use for Titled Card
.dx-card.dx-card-w-title {
    padding-top: 0px;
    h1 {
        color: #777777;
        font-size: 1rem;
        font-weight: 700;
        border-bottom: 1px solid #efefef;
        padding: 14px 0;
        margin: 0 0 14px 0;
    }
}

.toast div.dx-toast-message {
    font-size: 1rem;
    font-weight: 500;
}

#toast-container {
    font-family: Roboto;
}

.toast-container .ngx-toastr {
    width: 450px;
}

.toast-success {
    background-color: $base-success;
}

.toast-error {
    background-color: $base-danger;
}

.toast-warning {
    background-color: $base-warning;
}

.toast-info {
    background-color: $base-accent;
}

span.badge-text {
    background-color: red;
    color: white;
    padding: 4px 8px;
    text-align: center;
    border-radius: 5px;
    white-space: nowrap;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.24);
}

span.badge-content {
    position: relative;
    text-align: center;
    display: inline-block;
    border-radius: 50%;
    transition: transform 0.2s ease-in-out;
    transform: scale(0.6);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    pointer-events: none;
    background-color: #f44336;
    color: white;
    width: 1.7em;
    height: 1.7em;
    line-height: 1.7em;
    font-weight: 600;
    font-size: 0.8em;
    right: 0.5em;
}

.dx-card {
    h3,
    h4 {
        margin-top: 0;
    }
}
