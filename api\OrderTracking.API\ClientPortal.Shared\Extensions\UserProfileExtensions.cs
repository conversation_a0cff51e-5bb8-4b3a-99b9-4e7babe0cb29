using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using ClientPortal.Shared.Models;

namespace ClientPortal.Shared.Extensions
{
    /// <summary>
    ///     Extension methods for a <see cref="UserProfile" /> instance.
    /// </summary>
    public static class UserProfileExtensions
    {
        private static readonly List<string> NonWorkDomains = new List<string>
        {
            "outlook",
            "gmail",
            "yahoo",
            "inbox",
            "icloud",
            "mail",
            "aol",
            "zoho",
            "yandex",
            "comcast"
        };

        /// <summary>
        ///     Checks if role is part of role collection on user profile.  Case-insensitive.
        /// </summary>
        /// <param name="user"></param>
        /// <param name="role"></param>
        /// <returns></returns>
        public static bool HasRole(this UserProfile user, string role)
        {
            if (user == null) throw new ArgumentNullException(nameof(user));
            return user.Roles.Any(r => string.Equals(r, role, StringComparison.InvariantCultureIgnoreCase));
        }

        /// <summary>
        ///     Checks if the UserProfile's Id (primary email) includes a domain that is not what
        ///     we consider a work email.
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public static bool HasWorkEmail(this UserProfile user)
        {
            if (user == null) throw new ArgumentNullException(nameof(user));

            try
            {
                var domainSherpa = GetDomainSherpaFromEmail(user.Id);
                var hasWorkEmail = !NonWorkDomains.Contains(domainSherpa, StringComparer.OrdinalIgnoreCase);

                // Add debugging for troubleshooting
                Console.WriteLine($"Email validation - Email: {user.Id}, Domain: '{domainSherpa}', NonWorkDomains: [{string.Join(", ", NonWorkDomains)}], HasWorkEmail: {hasWorkEmail}");

                return hasWorkEmail;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Email validation error for {user.Id}: {ex.Message}");
                return false; // Fail safe - if we can't parse the email, consider it invalid
            }
        }

        private static string GetDomainSherpaFromEmail(string email)
        {
            if (email == null) throw new ArgumentNullException(nameof(email));

            var atSymbolIndex = email.IndexOf('@', StringComparison.InvariantCultureIgnoreCase);
            if (atSymbolIndex < 0)
                throw new ArgumentException($"Invalid email format: {email}");

            var domains = email.Substring(atSymbolIndex + 1);
            var domainParts = domains.Split('.');

            if (domainParts.Length == 0)
                throw new ArgumentException($"Invalid domain format: {domains}");

            var domainSherpa = domainParts[0].ToLower(); // Ensure case-insensitive comparison
            return domainSherpa;
        }

        /// <summary>
        ///     returns a list of roles that a given admin can assign
        /// </summary>
        /// <param></param>
        /// <param name="adminUser"></param>
        /// <param name="roles"></param>
        /// <returns></returns>
        public static IEnumerable<Role> AssignableRoles(this UserProfile adminUser, IEnumerable<Role> roles)
        {
            if (adminUser == null)
                return new List<Role>();
            var adminRoles = adminUser.Roles.Where(s => s.ToLower().Contains("admin"));

            adminRoles = adminRoles.Select(role => role.ToLower());
            // return full list if user is app:admin
            if (adminUser.HasRole("app:admin"))
                return roles;

            var modules = (from role in adminRoles let position = role.IndexOf(":") select role.Substring(0, position))
                .ToList();
            // Return only roles associated with those admin groups

            var availableRoles = new List<Role>();

            foreach (var module in modules)
            {
                var localRoles = roles.Where(role => role.Id.ToLower().Contains(module)).ToList();
                availableRoles.AddRange(localRoles);
            }

            return availableRoles;
        }

        /// <summary>
        ///     returns a list of roles that a given admin can assign
        /// </summary>
        /// <param></param>
        /// <param name="adminUser"></param>
        /// <param name="role"></param>
        /// <returns></returns>
        public static IEnumerable<Role> AssignableRoles(this UserProfile adminUser, Role role)
        {
            var roles = new[] {role};
            return adminUser.AssignableRoles(roles);
        }

        /// <summary>
        ///     returns a list of roles that a given admin can assign
        /// </summary>
        /// <param></param>
        /// <param name="adminUser"></param>
        /// <param name="role"></param>
        /// <returns></returns>
        public static IEnumerable<Role> AssignableRoles(this UserProfile adminUser, string role)
        {
            var roles = new[] {new Role(role)};
            return adminUser.AssignableRoles(roles);
        }

        /// <summary>
        ///     Returns true if the user profile's districtIds contain all of the provided district ids
        /// </summary>
        /// <param name="userProfile"></param>
        /// <param name="districtIds"></param>
        /// <returns></returns>
        public static bool HasDistricts(this UserProfile userProfile, IEnumerable<string> districtIds)
        {
            return districtIds.All(district => userProfile.DistrictIds.Contains(district));
        }

        /// <summary>
        ///     Returns true if the user profile has any role that is an admin level for any module/role-group
        /// </summary>
        /// <param name="userProfile"></param>
        /// <returns></returns>
        public static bool IsModuleAdmin(this UserProfile userProfile)
        {
            if (userProfile == null) throw new ArgumentNullException(nameof(userProfile));
            return userProfile.Roles.Any(role => role.Split(':').Last().ToLower() == "admin");
        }

        public static bool IsOnlyUserAgreementChanges(this UserProfile original, UserProfile updated)
        {
            if (original == null) throw new ArgumentNullException(nameof(original));
            if (updated == null) throw new ArgumentNullException(nameof(updated));

            var changedPropertyNames = FindNamesOfPropertiesThatChanged(original, updated);

            var disclaimerDateUpdated = changedPropertyNames.Contains(nameof(UserProfile.AcceptedDisclaimerDate));
            var clientDisclaimerVersionUpdated =
                changedPropertyNames.Contains(nameof(UserProfile.AcceptedClientDisclaimerVersion));
            var updatedDisclaimerVersionAndDate = disclaimerDateUpdated && clientDisclaimerVersionUpdated &&
                                                  changedPropertyNames.Count == 2;
            var acceptedDisclaimer = changedPropertyNames.Contains(nameof(UserProfile.AcceptedAgreement)) &&
                                     disclaimerDateUpdated && clientDisclaimerVersionUpdated &&
                                     changedPropertyNames.Count == 3;
            return updatedDisclaimerVersionAndDate || acceptedDisclaimer;
        }

        public static bool IsOnlyPermittedChanges(this UserProfile original, UserProfile updated)
        {
            if (original == null) throw new ArgumentNullException(nameof(original));
            if (updated == null) throw new ArgumentNullException(nameof(updated));

            var changedPropertyNames = FindNamesOfPropertiesThatChanged(original, updated);

            var zeroPropertiesChanged = changedPropertyNames.Count == 0;
            
            var oneOfAllowedPropertiesChanged =
                (changedPropertyNames.Contains(nameof(UserProfile.LastLoginDate)) ||
                 changedPropertyNames.Contains(nameof(UserProfile.LastClientPortalVersion)))
                && changedPropertyNames.Count == 1;

            var bothAllowedPropertiesChanged =
                changedPropertyNames.Contains(nameof(UserProfile.LastLoginDate)) &&
                changedPropertyNames.Contains(nameof(UserProfile.LastClientPortalVersion)) &&
                changedPropertyNames.Count == 2;
            
            var isOnlyPermittedChanges = zeroPropertiesChanged ||
                                         oneOfAllowedPropertiesChanged ||
                                         bothAllowedPropertiesChanged;
            return isOnlyPermittedChanges;
        }

        private static List<string> FindNamesOfPropertiesThatChanged(UserProfile original, UserProfile updated)
        {
            var changedPropertyNames = new List<string>();
            foreach (var property in original.GetType().GetProperties())
            {
                var originalValue = property.GetValue(original);
                var newValue = property.GetValue(updated);
                if (originalValue == null && newValue == null) continue;
                if (originalValue != null && newValue == null || originalValue == null && newValue != null)
                {
                    changedPropertyNames.Add(property.Name);
                    continue;
                }

                var genericType = originalValue.GetType().GetGenericArguments().SingleOrDefault();

                if (genericType == typeof(string))
                {
                    if (CheckCollectionProperty<string>(originalValue, newValue, changedPropertyNames, property))
                        continue;
                }
                else if (genericType == typeof(long))
                {
                    if (CheckCollectionProperty<long>(originalValue, newValue, changedPropertyNames, property))
                        continue;
                }

                if (!originalValue.Equals(newValue)) changedPropertyNames.Add(property.Name);
            }

            return changedPropertyNames;
        }

        private static bool CheckCollectionProperty<T>(object originalValue, object newValue,
            ICollection<string> changedPropertyNames,
            MemberInfo property)
        {
            if (originalValue is ICollection<T> && !(newValue is ICollection<T>) ||
                !(originalValue is ICollection<T>) && newValue is ICollection<T>)
            {
                changedPropertyNames.Add(property.Name);
                return true;
            }

            if (!(originalValue is ICollection<T> collection) || !(newValue is ICollection<T>)) return false;

            if (collection.Count != ((ICollection<T>) newValue).Count)
            {
                changedPropertyNames.Add(property.Name);
                return true;
            }

            var foundDifference =
                collection.Any(item => !((ICollection<T>) newValue).Contains(item));

            if (foundDifference) changedPropertyNames.Add(property.Name);
            return true;
        }
    }
}