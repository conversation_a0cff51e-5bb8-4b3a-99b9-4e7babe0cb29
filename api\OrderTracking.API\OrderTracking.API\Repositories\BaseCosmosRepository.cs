using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Base repository for Azure Cosmos DB operations (migrated from Firebase)
    /// </summary>
    public abstract class BaseCosmosRepository<TEntity, TKey> : IAsyncCosmosRepository<TEntity, TKey>
        where TEntity : ICosmosEntity<TKey>
    {
        protected Container Container { get; }
        protected string PartitionKeyPath { get; }

        protected BaseCosmosRepository(Container container, string partitionKeyPath)
        {
            Container = container ?? throw new ArgumentNullException(nameof(container));
            PartitionKeyPath = partitionKeyPath ?? throw new ArgumentNullException(nameof(partitionKeyPath));
        }

        public virtual async Task<TEntity> AddAsync(TEntity entity)
        {
            try
            {
                var response = await Container.CreateItemAsync(entity, GetPartitionKey(entity));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            try
            {
                var query = Container.GetItemQueryIterator<TEntity>();
                var results = new List<TEntity>();
                
                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    results.AddRange(response.ToList());
                }
                
                return results;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> GetAsync(TKey entityId);

        public virtual async Task<TEntity> GetAsync(TKey id, string partitionId)
        {
            try
            {
                var response = await Container.ReadItemAsync<TEntity>(id.ToString(), new PartitionKey(partitionId));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                Console.WriteLine($"Item not found - ID: {id}, PartitionId: {partitionId}, Container: {Container.Id}");

                // Try with different casing as fallback (for migration compatibility)
                var result = await TryGetWithDifferentCasing(id, partitionId);
                if (result != null)
                {
                    Console.WriteLine($"Found item with different casing - ID: {id}, PartitionId: {partitionId}");
                    return result;
                }

                return default;
            }
            catch (CosmosException ex)
            {
                Console.WriteLine($"Cosmos DB error - StatusCode: {ex.StatusCode}, ID: {id}, PartitionId: {partitionId}, Container: {Container.Id}, Message: {ex.Message}");
                throw;
            }
            catch (Exception e)
            {
                Console.WriteLine($"General error in GetAsync - ID: {id}, PartitionId: {partitionId}, Container: {Container.Id}, Error: {e}");
                throw;
            }
        }

        /// <summary>
        /// Try to get the item with different casing combinations (for migration compatibility)
        /// </summary>
        private async Task<TEntity> TryGetWithDifferentCasing(TKey id, string partitionId)
        {
            var caseCombinations = new[]
            {
                (id.ToString().ToLower(), partitionId.ToLower()),
                (id.ToString().ToUpper(), partitionId.ToUpper()),
                (id.ToString().ToLower(), partitionId),
                (id.ToString(), partitionId.ToLower())
            };

            foreach (var (idVariant, partitionVariant) in caseCombinations)
            {
                try
                {
                    var response = await Container.ReadItemAsync<TEntity>(idVariant, new PartitionKey(partitionVariant));
                    Console.WriteLine($"Found with casing variant - Original ID: {id}, Used ID: {idVariant}, Original Partition: {partitionId}, Used Partition: {partitionVariant}");
                    return response.Resource;
                }
                catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    // Continue to next combination
                    continue;
                }
            }

            // Last resort: try query-based search
            return await TryGetWithQuery(id, partitionId);
        }

        /// <summary>
        /// Try to find the item using a query (slower but more flexible)
        /// </summary>
        private async Task<TEntity> TryGetWithQuery(TKey id, string partitionId)
        {
            try
            {
                // First try with partition key constraint
                var query = Container.GetItemQueryIterator<TEntity>(
                    $"SELECT * FROM c WHERE c.id = '{id}' OR LOWER(c.id) = '{id.ToString().ToLower()}'",
                    requestOptions: new QueryRequestOptions
                    {
                        PartitionKey = new PartitionKey(partitionId)
                    });

                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    var item = response.FirstOrDefault();
                    if (item != null)
                    {
                        Console.WriteLine($"Found via query with partition - ID: {id}, PartitionId: {partitionId}");
                        return item;
                    }
                }

                // If not found with partition key, try cross-partition query (expensive but thorough)
                Console.WriteLine($"Attempting cross-partition search for ID: {id}");
                return await TryGetWithCrossPartitionQuery(id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Query fallback failed - ID: {id}, PartitionId: {partitionId}, Error: {ex.Message}");
            }

            return default;
        }

        /// <summary>
        /// Last resort: cross-partition query to find the item anywhere in the container
        /// </summary>
        private async Task<TEntity> TryGetWithCrossPartitionQuery(TKey id)
        {
            try
            {
                var query = Container.GetItemQueryIterator<TEntity>(
                    $"SELECT * FROM c WHERE c.id = '{id}' OR LOWER(c.id) = '{id.ToString().ToLower()}' OR UPPER(c.id) = '{id.ToString().ToUpper()}'",
                    requestOptions: new QueryRequestOptions
                    {
                        MaxItemCount = 1 // Limit to 1 item to reduce cost
                    });

                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    var item = response.FirstOrDefault();
                    if (item != null)
                    {
                        Console.WriteLine($"Found via cross-partition query - Searched ID: {id}, Found item with ID: {item.Id}");
                        return item;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Cross-partition query failed - ID: {id}, Error: {ex.Message}");
            }

            return default;
        }

        public virtual async Task RemoveAsync(TKey id)
        {
            try
            {
                // For simple removal, we need to get the item first to determine partition key
                var entity = await GetAsync(id);
                if (entity != null)
                {
                    await Container.DeleteItemAsync<TEntity>(id.ToString(), GetPartitionKey(entity));
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task RemoveAsync(TKey id, string partitionId);

        public virtual async Task RemoveAsync(TEntity entity)
        {
            try
            {
                await Container.DeleteItemAsync<TEntity>(entity.Id.ToString(), GetPartitionKey(entity));
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity)
        {
            try
            {
                var response = await Container.ReplaceItemAsync(entity, entity.Id.ToString(), GetPartitionKey(entity));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);

        public async Task<TEntity> UpdateAsync(TEntity entity)
        {
            try
            {
                var response = await Container.ReplaceItemAsync(entity, entity.Id.ToString(), GetPartitionKey(entity));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        /// <summary>
        /// Get the partition key for an entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        protected virtual PartitionKey GetPartitionKey(TEntity entity)
        {
            // Default implementation uses the entity ID as partition key
            // Override in derived classes if different partition key logic is needed
            return new PartitionKey(entity.Id.ToString());
        }

        /// <summary>
        /// Diagnostic method to find all items that might match the given ID pattern
        /// Use this for troubleshooting missing accounts
        /// </summary>
        public async Task<List<TEntity>> DiagnoseAccountAsync(TKey id)
        {
            var results = new List<TEntity>();
            var searchPatterns = new[]
            {
                id.ToString(),
                id.ToString().ToLower(),
                id.ToString().ToUpper(),
                id.ToString().Replace("-", ""),
                id.ToString().Replace("_", "")
            };

            Console.WriteLine($"=== DIAGNOSTIC SEARCH FOR ACCOUNT: {id} ===");
            Console.WriteLine($"Container: {Container.Id}");
            Console.WriteLine($"Partition Key Path: {PartitionKeyPath}");

            foreach (var pattern in searchPatterns.Distinct())
            {
                try
                {
                    var query = Container.GetItemQueryIterator<TEntity>(
                        $"SELECT * FROM c WHERE CONTAINS(LOWER(c.id), LOWER('{pattern}'))",
                        requestOptions: new QueryRequestOptions
                        {
                            MaxItemCount = 10 // Limit results
                        });

                    while (query.HasMoreResults)
                    {
                        var response = await query.ReadNextAsync();
                        foreach (var item in response)
                        {
                            Console.WriteLine($"Found potential match - ID: {item.Id}, Pattern: {pattern}");
                            results.Add(item);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error searching pattern '{pattern}': {ex.Message}");
                }
            }

            Console.WriteLine($"=== DIAGNOSTIC COMPLETE - Found {results.Count} potential matches ===");
            return results;
        }
    }
}
